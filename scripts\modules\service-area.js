/**
 * Service Area Map Module
 * Handles the initialization and management of the service area map
 */

export function initServiceArea() {
    const mapContainer = document.getElementById('service-area-map');
    if (!mapContainer) return null;

    // Initialize map when the page is shown
    const initMap = () => {
        // Ocala coordinates
        const ocalaCenter = { lat: 29.1872, lng: -82.1401 };
        
        // Create the map
        const map = new google.maps.Map(mapContainer, {
            center: ocalaCenter,
            zoom: 12,
            styles: [
                {
                    "featureType": "all",
                    "elementType": "geometry",
                    "stylers": [{"color": "#f5f5f5"}]
                },
                {
                    "featureType": "water",
                    "elementType": "geometry",
                    "stylers": [{"color": "#e9e9e9"}, {"lightness": 17}]
                }
            ]
        });

        // Define the service area polygon
        const serviceArea = new google.maps.Polygon({
            paths: [
                // These coordinates should be adjusted to match your actual service area
                { lat: 29.1972, lng: -82.1501 },
                { lat: 29.1972, lng: -82.1301 },
                { lat: 29.1772, lng: -82.1301 },
                { lat: 29.1772, lng: -82.1501 }
            ],
            strokeColor: '#FF0000',
            strokeOpacity: 0.8,
            strokeWeight: 2,
            fillColor: '#FF0000',
            fillOpacity: 0.35
        });

        serviceArea.setMap(map);
    };

    // Load Google Maps API
    const loadGoogleMapsAPI = () => {
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap`;
        script.async = true;
        script.defer = true;
        window.initMap = initMap;
        document.head.appendChild(script);
    };

    // Initialize when the service area page is shown
    const serviceAreaPage = document.querySelector('.service-area-page');
    if (serviceAreaPage) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.target.classList.contains('active')) {
                    loadGoogleMapsAPI();
                }
            });
        });

        observer.observe(serviceAreaPage, {
            attributes: true,
            attributeFilter: ['class']
        });
    }

    // Cleanup function
    const cleanup = () => {
        if (window.initMap) {
            delete window.initMap;
        }
    };

    return {
        cleanup
    };
} 