@import url('https://fonts.googleapis.com/css?family=Lato:300,400|Poppins:300,400,800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200..800&display=swap');


/* ======================================================
BODY STYLES
====================================================== */
body {
margin: 0;
padding: 0;
font-family: Arial, sans-serif;
background: linear-gradient(to top left, #4fd1c5, #e6fffa);
min-height: 100vh;
display: flex;
align-items: center;
justify-content: center;
}

/* ======================================================
CARD STYLES
====================================================== */

.card-container {
z-index: 9999;
display: flex;
align-items: center;
justify-content: center;
width: 100%;
position: absolute;
top: 210px;
left: 50%;
transform: translateX(-50%) scale(1) rotateX(0deg) rotateY(0deg);
perspective: 900px;
pointer-events: none;
opacity: 1;
/*animation: 0.9s ease-in 0s 1 both cardintro;*/
}

@keyframes cardintro {
0% {
opacity: 1;
transform: translateX(-100%) scale(0) rotateX(-45deg) rotateY(-180deg);
top: 0px;
}
100% {
opacity: 1;
transform: translateX(-50%) scale(1)  rotateX(0deg) rotateY(0deg);
top: 210px;
}
}

.card {
width: 100%;
max-width: 500px;
height: 310px;
position: relative;
background: #bbb;
padding: 0; /* We'll handle inner spacing via content layers */
transform-style: preserve-3d;
transition: height 0.5s cubic-bezier(0.25, 0.1, 0.25, 1), transform 0.05s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s ease;
pointer-events: auto;
}
/* MAIN PAGE SIZE */
.card:hover {
height: 350px;
box-shadow: 0 0 25px 8px rgba(255, 255, 255, 0.7);
}
.card-inner {
position: absolute;
inset: 1px;
box-shadow: 0 35px 50px -12px rgba(0, 0, 0, 0.25);
overflow: hidden; /* Clip shine effects to card boundaries */
}

.card-border {
position: absolute;
inset: 0;
overflow: hidden;
z-index: 0;
}

.card-background {
position: absolute;
inset: 0;
background-color: #eee;
}

.card-content {
position: absolute;
inset: 16px;
background-color: hsl(184, 100%, 60%);
padding: 1.25rem; /* inner breathing room */
display: flex;
flex-direction: column;
justify-content: space-between;
box-sizing: border-box;
overflow: hidden;
border: 1px solid #aaa;
}

.card-content::before {
content: '';
position: absolute;
inset: 4px;
background: #eee;
z-index: 1;
box-sizing: border-box;
border: 1px solid #aaa;
}

.card-header {
position: relative;
z-index: 2;
padding-bottom: 0.5rem;
}

/* ======================================================
MAIN PAGE TEXT SETTINGS
====================================================== */
h1 {
font-size: 1.75rem;
font-weight: bold;
color: #000;
font-family: serif;
margin: 0;
letter-spacing: -0.045em;
text-shadow: -1px -1px rgba(0,0,0,0.4), 1px 1px rgba(255,255,255,1);
font-family: 'Poppins';
font-size: 32px;
background-color: #666666;
text-shadow: 1px 1.01px 1.01px #555;
-webkit-background-clip: text;
-moz-background-clip: text;
background-clip: text;
animation: 0.9s ease-in 0s 1 both h1-intro;
}

@keyframes h1-intro {
0% {
opacity: 0;
transform:  translateX(-0%) rotateX(45deg) ;
}
100% {
opacity: 1;
transform: translateX(-0%) rotateX(0deg);
}
}
.subtitle {
font-size: 1.125rem;
font-family: serif;
text-shadow: -1px -1px rgba(0,0,0,0.4), 1px 1px rgba(255,255,255,1);
color: rgba(0,200,200,0.4);
background-color: #666666;
text-shadow: 1px 1.01px 1.01px #555;
-webkit-background-clip: text;
-moz-background-clip: text;
background-clip: text;
font-weight: 400;
font-family: 'Lato';
font-size: 12px;
letter-spacing: 1.3px;
margin-top: -4px;
color: hsl(184, 100%, 35%);
animation: 1.3s 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.25) both subtitle-intro;
position: relative;
}

@keyframes subtitle-intro {
0% {
left: 4%;
opacity: 0;
transform:  translateX(-0%) rotateX(45deg) ;
}
100% {
left: 0%;
opacity: 1;
transform: translateX(-0%) rotateX(0deg);
}
}
.name {
font-size: 1.1rem;
color: #4a5568;
margin-top: 1.125rem;
font-family: serif;
text-transform: uppercase;
letter-spacing: 0.05em;
color: #eee;
background-color: #666666;
text-shadow: 1px 1.01px 1.01px #555;
-webkit-background-clip: text;
-moz-background-clip: text;
background-clip: text;
transition: all 0.3s ease;
animation: 1.3s 0.9s cubic-bezier(0.68, -0.55, 0.265, 1.25) both name-intro;
position: relative;
}

@keyframes name-intro {
0% {
text-shadow: 1px 1.01px 1.01px #0fe5f5;
left: 4%;
opacity: 0;
}
100% {
text-shadow: 1px 1.01px 1.01px #555;
left: 0%;
opacity: 1;
}
}
.card-content:hover .name {
color: #444;
background-color: #666;
text-shadow: 1px 1.01px 1.01px #777;
color: transparent;
}

/* ======================================================
ABOUT ME BUTTON STYLES, SERVICES BUTTON & MA NUMBER
====================================================== */
/* About Me Container / MA Number */
.about-me-container {
/* Layout */
display: flex;
justify-content: left;
align-items: center;
gap: 10px;
color: hsl(0, 0%, 40%);
background-color: #666;
/* Font settings */
font-weight: 400;
font-family: 'Lato';
font-size: 12px;
text-transform: uppercase;
letter-spacing: 0.2em;
text-shadow: 1px 1.01px 1.01px #777;
-webkit-background-clip: text;
-moz-background-clip: text;
background-clip: text;
/* Spacing */
margin-top: -30px;
position: relative;
/* Animations */
transition: all 0.3s cubic-bezier(0.6, -0.28, 0.735, 0.045);
opacity: 0;
transform: translateY(-30%) rotateX(45deg);
filter: blur(4px);
}
/* Drop down Hover Animations */
.about-me-container:hover,
.name:hover + .about-me-container {
filter: blur(0px);
opacity: 1;
transform: translateY(-0%) rotateX(0deg);
}
/* About Me Button*/
.about-me { /* Button Container */
margin-right: 0%;
}
.about-me span { /* Hidden Text */
display:none;
}
.about-me:after { /* Current Text */
content: '✾ About Me';
}
.about-me:hover::after { /* Hover Text */
color:#4fd1c5;
content: '✿ About Me';
}
.about-me:active::after { /* Active Text */
color:#4fd1c5;
content: '❉ About Me';
}
/* Services Button */
.services { /* Button Container */
margin-left: 0%;
}
.services span { /* Hidden Text */
display:none;
}
.services:after { /* Current Text */
content: '✾ Services';
}
.services:hover::after { /* Hover Text */
color:#4fd1c5;
content: '✿ Services';
}
.services:active::after { /* Active Text */
color:#4fd1c5;
content: '❉ Services';
}
/* MA Number */
.MA-number { /* MA Number */
margin-left: -0%;
}
.MA-number span { /* Hidden Text */
display:none;
}
.MA-number:after { /* Current Text */
content: '✾ MA#83097';
}
.MA-number:hover::after { /* Hover Text */
color:#4fd1c5;
content: '✿ MA#83097';
}
.MA-number:active::after { /* Active Text */
color:#4fd1c5;
content: '❉ MA#83097';
}



/* ======================================================
CONTACT INFO TEXT/ICON STYLES AND ANIMATIONS
====================================================== */
.contact-info {
margin-top: 0.75rem;
display: flex;
flex-direction: column;
gap: 0.125rem;
position: absolute;
top: 135px;
z-index: 2;
}

.contact-item {
display: flex;
align-items: center;
gap: 0.5rem;
transition: transform 0.3s ease, box-shadow 0.3s ease, filter 0.3s ease;
padding: 5px 60px 5px 5px; /* Extended left padding to create larger hover area */
border-radius: 4px;
position: relative;
animation: 1.3s 1s cubic-bezier(0.68, -0.55, 0.265, 1.25) both contact-intro;
}

.cti-one {
animation-delay: 1.7s;
}
.cti-two {
animation-delay: 2.2s;
}
.cti-three {
animation-delay: 2.7s;
}

@keyframes contact-intro {
0% {
left: -6%;
opacity: 0;
transform:  translateY(-0%)  ;
}
100% {
left: 0%;
opacity: 1;
transform: translateY(-0%);
}
}
/* When any contact item is hovered, blur all contact items */
.contact-info:hover .contact-item {
filter: blur(2px) contrast(3) brightness(3) invert(40%) hue-rotate(-180deg) ;
opacity: 0.2;
}

/* But keep the actually hovered item clear */
.contact-info .contact-item:hover {
filter: blur(0) contrast(1) brightness(1);
opacity: 1;
}

.contact-item:hover {
transform: translateY(-3px);
}

.icon {
height: 1.125rem;
width: 1.125rem;
color: hsl(184, 100%, 30%);
flex-shrink: 0;
transition: transform 0.3s ease, color 0.3s ease;
}

.contact-item:hover .icon {
transform: scale(1.2) rotate(0deg);
color: hsl(184, 100%, 50%);
animation: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.25) icon-rotate both;
}

@keyframes icon-rotate {
0% {
transform: scale(1.2) rotate(0deg);
}
25% {
transform: scale(1.2) rotate(10deg);
}
50% {
transform: scale(1.2) rotate(0deg);
}
75% {
transform: scale(1.2) rotate(-10deg);
}
100% {
transform: scale(1.2) rotate(0deg);
}
}

.contact-item span {
font-size: 0.875rem;
font-family: sans-serif;
text-shadow: 1px 1.01px 1.01px #aaa;
-webkit-background-clip: text;
-moz-background-clip: text;
background-clip: text;
transition: color 0.3s ease;
color: hsl(0, 0%, 40%);
font-weight: 600;
font-family: 'Lato';
font-size: 14px;
letter-spacing: 0.2px;

transition: color 0.3s ease, font-size 0.3s ease;
}

.contact-item:hover span {
color: hsl(0, 0%, 35%);
font-size: 16px;
font-weight: 900;
}

/* ======================================================
APPOINTMENT BUTTON STYLES AND ANIMATIONS
====================================================== */

/**
* Book Appointment Button
*
* Initially appears as a small, blurred circle at the bottom of the card.
* When the card is hovered, it transforms into a full-width button with text.
*
* Animation sequence:
* 1. Hidden state: Small circular button (100px), blurred, positioned below card
* 2. Card hover: Expands to full width, moves to top position, becomes visible
* 3. Button hover: Changes color, adds subtle pulsing animation
* 4. Button active: Adds glow effect and changes border colors
*/
.book-button {
/* Stacking Context and Basic Layout */
z-index: 2;
display: inline-block;
cursor: pointer;
position: relative;
overflow: hidden; /* Prevents text from showing during initial state */

/* Initial Dimensions and Positioning */
width: 100px; /* Initial circular shape width */
height: auto; /* Maintains circular proportion */
top: 90%; /* Positioned near bottom of container */
left: 50%; /* Centered horizontally */
transform: translate(-50%, 150%) scale(0.5); /* Positioned below card, scaled down */
padding: 0.6rem;
border-radius: 555px; /* Large value ensures circular shape */

/* Initial Visual State */
opacity: 0; /* Hidden initially */
filter: blur(80px); /* Heavy blur effect when hidden */
color: hsl(0, 0%, 90%); /* Gray text */
background-color: hsl(184, 100%, 40%); /* Teal base color */

/* Border Styling - Multi-tone borders create depth */
border-top: hsl(184, 100%, 40%) 1px solid; /* Darker top border */
border-bottom: hsl(184, 100%, 55%) 6px solid; /* Lighter, thicker bottom border */
border-left: hsl(184, 100%, 55%) 2px solid; /* Lighter side borders */
border-right: hsl(184, 100%, 55%) 2px solid;
box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.0); /* No initial glow */

/* Transition Properties - Controls animation timing and easing */
transition:
width 0.4s ease-in-out, /* Faster width transition */
left 0.4s ease-in-out,
height 0.4s ease-in-out,
background-color 0.2s ease-in-out, /* Quick color changes */
opacity 0.4s ease-in-out, /* Smooth fade in/out */
transform 0.4s ease-in-out, /* Simpler transform transition */
filter 0.4s ease-in-out, /* Smooth blur transition */
box-shadow 0.3s ease-in-out; /* Smooth glow transition */
}

/**
* Button Appearance When Card is Hovered
*
* Transforms from hidden circular button to visible rectangular button
* Positioned at the top of the card with full width
*/
.card:hover .book-button {
/* New Dimensions and Positioning */
width: 100%; /* Expands to full width */
height: 50px; /* Fixed height for button */
top: 0%; /* Moves to top of container */
transform: translate(-50%, -3%) scale(1); /* Slight upward shift, normal scale */

/* Visible State */
opacity: 1; /* Fully visible */
filter: blur(0px); /* No blur */
box-shadow: 0px 0px 0px 0px hsl(184, 10%, 40%); /* No shadow initially */

/* Same transition properties to ensure consistent animation */
transition:
width 0.8s cubic-bezier(0.6, -0.28, 0.735, 0.045), /* Back ease-in for width expansion - slightly faster */
left 0.8s ease,
height 0.8s ease,
background-color 0.02s ease-in-out, /* Quick color changes */
opacity 0.6s ease-in-out, /* Smooth fade in/out */
transform 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55), /* Elastic/spring effect */
filter 0.35s ease-in-out, /* Smooth blur transition */
box-shadow 0.3s ease-in-out; /* Smooth glow transition */
}

/**
* Button Hover State
*
* Applies when user hovers directly over the button
* Darkens the button color and adds a subtle pulsing animation
*/
.book-button:hover {
/* Darker color scheme on hover */
background-color: hsl(184, 100%, 35%); /* Darker teal */
border-bottom: hsl(184, 100%, 30%) 6px solid; /* Darker borders */
border-left: hsl(184, 100%, 30%) 2px solid;
border-right: hsl(184, 100%, 30%) 2px solid;

/* Applies the pulsing animation */
animation: buttonhover 1.5s ease-in-out infinite;
}

/**
* Button Active State (When Clicked)
*
* Changes appearance when button is being clicked
* Adds glow effect and lightens borders for visual feedback
*/
.book-button:active {
/* Maintain expanded dimensions */
width: 100%;
height: 50px;
top: 0%;
transform: scale(1); /* No scaling effect */

/* Visual feedback for click */
background-color: hsl(184, 100%, 45%); /* Lighter teal */
box-shadow: 0px 0px 16px 6px hsl(184, 100%, 30%); /* Prominent glow effect */

/* Lighter borders create pressed appearance */
border-top: hsl(184, 100%, 70%) 1px solid; /* Much lighter top */
border-bottom: hsl(184, 100%, 60%) 3px solid; /* Thinner bottom border */
border-left: hsl(184, 100%, 60%) 2px solid;
border-right: hsl(184, 100%, 60%) 2px solid;
}

/**
* Button Text Styling and Animation
*
* Controls the appearance and reveal animation of the button text
* Text slides in from right to left with a blur effect
*/
.book-button span {
/* Text styling */
text-decoration: none;
text-transform: uppercase;
font-family: 'Lato';
text-align: center;
font-weight: 900; /* Bold text */
font-size: 16px;
letter-spacing: 3px; /* Spaced out letters */

/* Positioning */
display: block;
position: relative;
z-index: 1; /* Ensures text appears above any effects */
opacity: 0; /* Start invisible */
right: 355px; /* Start position */

/* Transition for manual control */
transition: opacity 0.4s ease-in-out, right 0.4s ease-in-out, filter 0.4s ease-in-out;
}

/* Apply animation when card is hovered */
.card:hover .book-button span {
animation: appt-text-reveal 1.4s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards; /* Spring effect */
}

/**
* Button Text Hover State
*
* Changes text color when hovering directly over the button
*/
.book-button:hover span {
color: hsl(0, 0%, 100%); /* Dark gray text on hover */
font-size: 16px;
font-weight: 900;
}

/**
* Button Hover Animation
*
* Creates a subtle pulsing effect when hovering over the button
* Button slightly scales up and down to create a breathing effect
*/
@keyframes buttonhover {
0% {
transform: translate(-50%, -3%) scale(1); /* Normal size */
}
50% {
transform: translate(-50%, -4%) scale(1.01); /* Slightly larger */
}
100% {
transform: translate(-50%, -3%) scale(1); /* Back to normal */
}
}

/**
* Text Reveal Animation
*
* Animates the button text from right to left with a blur effect
* Text starts off-screen to the right and slides into position
*/
@keyframes appt-text-reveal {
0% {
opacity: 0; /* Start invisible */
right: 355px; /* Start position far to the right */
filter: blur(6px); /* Initially blurred */
}
10% {
opacity: 1; /* Fade in quickly */
}
100% {
opacity: 1;
right: 0px; /* Final position */
filter: blur(0px); /* Clear and sharp */
}
}


/* ======================================================
SHINE EFFECTS - LIGHT REFLECTION ANIMATIONS
====================================================== */

/**
* Shine Effects Overview
*
* These elements create dynamic light reflection effects that follow mouse movement
* across the business card. Three different shine bars with varying thickness and
* opacity create a layered, realistic light reflection effect.
*
* All shine effects:
* - Follow mouse movement horizontally across the card
* - Rotate at 45/-45 degree angles based on mouse position
* - Appear/disappear with smooth transitions
* - Are clipped to the card boundaries
*/

/**
* Primary Shine Effect - Medium Thickness
*
* Creates a medium-width light reflection that follows mouse movement
* Provides the main highlight effect across the card surface
*/
.shine-thick {
position: absolute;
width: 120%; /* Wider than card to ensure full coverage during rotation */
height: 30px; /* Medium thickness */
background-color: rgba(255, 255, 255, 0.5); /* Semi-transparent white */
transform-origin: center; /* Rotation happens from center point */
pointer-events: none; /* Doesn't interfere with mouse interactions */
opacity: 0; /* Hidden by default, shown on hover */
transition: opacity 0.3s ease, left 0.1s ease-out; /* Smooth transitions */
z-index: 10; /* Appears above card background but below content */
mix-blend-mode: overlay; /* Creates realistic light interaction with colors beneath */
box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.4); /* Soft glow around the shine */
}

/**
* Secondary Shine Effect - Thin Line
*
* Creates a thin line of light reflection that follows mouse movement
* Adds a subtle secondary highlight that complements the main shine
*/
.shine-skinny {
position: absolute;
width: 120%; /* Wider than card to ensure full coverage during rotation */
height: 8px; /* Thin line */
background-color: rgba(255, 255, 255, 0.5); /* Semi-transparent white */
transform-origin: center; /* Rotation happens from center point */
pointer-events: none; /* Doesn't interfere with mouse interactions */
opacity: 0; /* Hidden by default, shown on hover */
transition: opacity 0.3s ease, left 0.1s ease-out; /* Smooth transitions */
z-index: 10; /* Appears above card background but below content */
mix-blend-mode: overlay; /* Creates realistic light interaction with colors beneath */
box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.4); /* Soft glow around the shine */
}

/**
* Tertiary Shine Effect - Diffused Glow
*
* Creates a wide, blurred light reflection that follows mouse movement
* Provides a soft ambient glow effect across the entire card
*/
.shine-extra-thick {
position: absolute;
width: 170%; /* Much wider than card for a broad effect */
height: 245px; /* Covers most of the card height */
background-color: rgba(255, 255, 255, 0.3); /* More transparent than other shines */
transform-origin: center; /* Rotation happens from center point */
pointer-events: none; /* Doesn't interfere with mouse interactions */
opacity: 0; /* Hidden by default, shown on hover */
transition: opacity 0.3s ease, left 0.1s ease-out; /* Smooth transitions */
z-index: 10; /* Appears above card background but below content */
mix-blend-mode: overlay; /* Creates realistic light interaction with colors beneath */
filter: blur(30px); /* Heavy blur creates soft, diffused effect */
}

.holographic-effect {
position: absolute;
inset: 0; /* Cover the entire card */
background-image: url(https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExMWl2eWhkZm9jMnl2b2R4YnR3ZGw0cmd1eTFtdThoYmRrdHV2NDR3YyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/WWSPhALYIBk1wtIwGZ/giphy.gif);
background-size: 100% 100%;
background-position: center;
mix-blend-mode: overlay;
opacity: 1; /* Start with a subtle effect */
filter: hue-rotate(0deg) brightness(1) blur(0px);
z-index: 3; /* Above card-background but below content */
pointer-events: none; /* Allow clicks to pass through */
}

.holographic-effect-grey {
position: absolute;
inset: 0; /* Cover the entire card */
background-image: url(https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExMWl2eWhkZm9jMnl2b2R4YnR3ZGw0cmd1eTFtdThoYmRrdHV2NDR3YyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/WWSPhALYIBk1wtIwGZ/giphy.gif);
background-size: 100%;
background-position: center;
mix-blend-mode: difference;
opacity: 0.3; /* Start with a subtle effect */
filter: grayscale(100%) invert(100%) hue-rotate(0deg) brightness(1) blur(0px);
z-index: 4; /* Above card-background but below content */
pointer-events: none; /* Allow clicks to pass through */
}
.holographic-effect-screen {
position: absolute;
inset: 0; /* Cover the entire card */
background-image: url(https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExMWl2eWhkZm9jMnl2b2R4YnR3ZGw0cmd1eTFtdThoYmRrdHV2NDR3YyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/WWSPhALYIBk1wtIwGZ/giphy.gif);
background-size: 100%;
background-position: center;
mix-blend-mode: screen;
opacity: 0.3; /* Start with a subtle effect */
filter: grayscale(100%) invert(100%) hue-rotate(0deg) brightness(1) blur(0px);
z-index: 5; /* Above card-background but below content */
pointer-events: none; /* Allow clicks to pass through */
}
.holographic-effect-sparkles {
position: absolute;
inset: 0; /* Cover the entire card */
background-image: url(https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExejZmZHBtbTl4ZjF5azcxa2l0eXg4eDR2MHM3ZWsxam15cWlmb3A3cCZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/dZS6D8OxCz8DHs7HbJ/giphy.gif);
background-size: 100%;
background-position: center;
mix-blend-mode: overlay;
opacity: 0.1; /* Start with a subtle effect */
filter: hue-rotate(0deg) brightness(1) blur(0px);
z-index: 6; /* Above card-background but below content */
pointer-events: none; /* Allow clicks to pass through */
}

.rainbow-gif {
position: absolute;
inset: 0; /* Cover the entire card */
background-image: url(https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExM3Rld2JzN2VhajQ3ZW9pMjh3cDV6Mmp1MWU1OHg5enNrN2RqeHB4dyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/2aQS3AHfvvfIkSdbFM/giphy.gif);
background-size: 120% 120%; /* Base size - will be scaled dynamically */
background-position: center;
mix-blend-mode: multiply;
opacity: 0.15; /* Start with a subtle effect */
filter: blur(0px) hue-rotate(0deg);
z-index: 66; /* Above card-background but below content */
pointer-events: none; /* Allow clicks to pass through */
transition: opacity 0.3s ease, transform 0.05s ease-out; /* Smooth transition for parallax movement */
transform-origin: center; /* Ensure scaling happens from the center */
}

/* Rainbow Sphere Light Source */
.rainbow-sphere {
position: absolute;
width: 350px;
height: 450px;
border-radius: 0%;
pointer-events: none;
z-index: 5;
opacity: 0;
transition: opacity 0.3s ease;
transform: translate(-50%, -50%);
background: linear-gradient(
45deg,
rgba(255, 255, 255, 1) 0%,
rgba(255, 0, 0, 0.75) 10%,
rgba(255, 165, 0, 0.7) 20%,
rgba(255, 255, 0, 0.65) 30%,
rgba(0, 255, 0, 0.65) 40%,
rgba(0, 255, 255, 0.6) 50%,
rgba(0, 0, 255, 0.5) 60%,
rgba(128, 0, 128, 0.4) 70%,
rgba(0, 0, 0, 0) 80%
);
mix-blend-mode: normal;
filter: blur(55px) contrast(1.8) saturate(1);
box-shadow: 0 0 30px 10px rgba(255, 255, 255, 0.3);
}

/* Glitter Reveal Layer */
.glitter-reveal {
position: absolute;
inset: 0;
background-image: url('assets/images/glitter.png');
background-size: 120% 120%;
background-position: center;
opacity: 0;
z-index: 6;
pointer-events: none;
mix-blend-mode: screen;
filter: contrast(1) brightness(1) hue-rotate(0deg);
mask-image: radial-gradient(circle at 50% 50%, black 0%, transparent 70%);
-webkit-mask-image: radial-gradient(circle at 50% 50%, black 0%, transparent 70%);
animation: shimmer 3s infinite linear;
}

/* VMAX Background Reveal Layer */
.vmaxbg-reveal {
position: absolute;
inset: 0;
background-image: url('assets/images/vmaxbg.jpg');
background-size: 50% 50%;
background-position: center;
opacity: 0;
z-index: 4;
pointer-events: none;
mix-blend-mode: overlay;
filter: contrast(1) brightness(1) hue-rotate(0deg);
mask-image: radial-gradient(circle at 50% 50%, black 0%, transparent 70%);
-webkit-mask-image: radial-gradient(circle at 50% 50%, black 0%, transparent 70%);
animation: shimmer 3s infinite linear;
}

/* Galaxy Reveal Layer */
.galaxy-reveal {
position: absolute;
inset: 0;
background-image: url('assets/images/cosmos-middle-trans.png');
background-size: 70% 70%;
background-position: center;
opacity: 0;
z-index: 6;
pointer-events: none;
mix-blend-mode: overlay;
filter: contrast(1) brightness(1) hue-rotate(0deg);
mask-image: radial-gradient(circle at 50% 50%, black 0%, transparent 60%);
-webkit-mask-image: radial-gradient(circle at 50% 50%, black 0%, transparent 60%);
animation: shimmer 3s infinite linear;
}

@keyframes shimmer {
0% {
filter: opacity(0.7) contrast(1.5) brightness(1.5) hue-rotate(0deg);
}
50% {
filter: opacity(1) contrast(1.8) brightness(1.8) hue-rotate(30deg);
}
100% {
filter: opacity(0.7) contrast(1.5) brightness(1.5) hue-rotate(0deg);
}
}

.adjustment-layer {
position: absolute;
inset: 0;
background-color: hsl(184, 100%, 60%);
mix-blend-mode: normal;
opacity: 1;
z-index: 1;
pointer-events: none;
transition: opacity 0.3s ease, background-color 0.3s ease, mix-blend-mode 0.3s ease;
}

.card:hover .adjustment-layer {
background-color: hsl(39, 100%, 55%);
opacity: 1;
mix-blend-mode: overlay;
}

/* MOUSE GRADIENT */
.mouse-gradient {
position: absolute;
width: 300px;
height: 300px;
border-radius: 50%;
background: radial-gradient(circle,
hsl(52 0% 90%) 0%,
hsl(51 99% 50%) 20%,
hsl(49 100% 50%) 40%,
hsl(46 100% 50%) 60%,
hsl(44 100% 50%) 80%);
transform: translate(-50%, -50%) rotate(0deg);
pointer-events: none;
filter: blur(80px) hue-rotate(0deg) contrast(1) brightness(1);
opacity: 0;
transition: opacity 0.3s ease;
z-index: 0;
mix-blend-mode: normal;
}

/* ======================================================
ABOUT ME PAGE & SERVICES PAGE
====================================================== */

/* ABOUT ME PAGE BUTTON - currently shows the page when clicked */
.about-me:active + .about-me-page {
pointer-events: auto;
opacity: 1;
}
/* ABOUT ME PAGE SIZE */
.card.about-me-active {
top: -100px; /* Adjust the top position */
height: 680px;  /* Adjust the height */
max-width: 800px;   /* Adjust the max-width */
}

.about-me-page {
position: absolute;
inset: 6px;
background-color:rgb(217, 241, 241);
opacity: 0;
z-index: 30;
pointer-events: none;
transition: opacity 0.3s ease;
mix-blend-mode: normal;
padding: 20px;
}
.about-me-text {
margin-top: 14px;
margin-left: 12px;
margin-right: 12px;
text-indent: 1em;
text-align: justify;
text-shadow: 1px 1.01px 1.01px #777;
background-color: #666;
-webkit-background-clip: text;
-moz-background-clip: text;
background-clip: text;
color: hsl(0, 0%, 40%);
font-weight: 400;
font-family: 'Lato';
font-size: 16px;
line-height: 1.5;
letter-spacing: 0.2px;
transition: color 0.3s ease;
position: relative;
}

/* SERVICES PAGE BUTTON - shows the page when clicked */
.services:active + .services-page {
pointer-events: auto;
opacity: 1;
}
/* SERVICES PAGE SIZE */
.card.services-active {
  top: -180px; /* Adjust the top position */
  height: 700px;  /* Increased height to fit content */
  max-width: 1100px;   /* Adjust the max-width */
  transition: all 0.5s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.services-page {
  position: absolute;
  inset: 6px;
  background-color: rgb(230, 245, 245);
  opacity: 0;
  z-index: 30;
  pointer-events: none;
  transition: opacity 0.4s ease, transform 0.4s ease;
  mix-blend-mode: normal;
  padding: 30px;
  border-radius: 8px;
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
}

/* Services header styling */
.services-header {
  max-width: 1000px;
  margin: 0 auto 20px;
  text-align: center;
}

/* Services container styling */
.services-container {
  display: flex;
  flex-direction: row; /* Horizontal layout */
  flex-wrap: wrap; /* Allow items to wrap to next row if needed */
  margin: 30px auto;
  padding: 0 20px;
  justify-content: center; /* Center items */
  gap: 20px; /* Increased gap between items for better spacing */
  max-width: 1200px;
}

/* Services item styling */
.services-item {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
  flex: 0 0 calc(33.333% - 30px); /* Set width to 1/3 minus gap */
  max-width: calc(33.333% - 30px);
  min-width: 280px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(79, 209, 197, 0.15);
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: 100%;
}

.services-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, hsl(184, 100%, 40%), hsl(184, 100%, 60%));
  opacity: 0.7;
  transition: height 0.3s ease;
}

.services-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
  background-color: rgba(255, 255, 255, 1);
}

.services-item:hover::before {
  height: 6px;
  opacity: 1;
}

.service-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 5px;
}

.service-header-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  justify-content: space-between;
  gap: 8px;
}

.services-item h2 {
  color: hsl(184, 100%, 30%);
  margin: 0;
  padding: 0;
  line-height: 1.3;
  font-family: "Manrope", sans-serif;
  font-optical-sizing: auto;
  font-weight: 800;
  font-style: normal;
  font-size: 1.1rem;
  letter-spacing: 0.5px;
  transition: color 0.3s ease;
}

.services-item:hover h2 {
  color: hsl(184, 100%, 40%);
}

.service-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 60px;
  height: 60px;
  background-color: rgba(79, 209, 197, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
  flex-shrink: 0;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
}

.services-item:hover .service-icon-container {
  background-color: rgba(79, 209, 197, 0.2);
  transform: scale(1.05);
  box-shadow: 0 5px 12px rgba(0, 0, 0, 0.08);
}

.service-icon {
  height: 2.2rem;
  width: 2.2rem;
  color: hsl(184, 100%, 30%);
  flex-shrink: 0;
  transition: transform 0.3s ease, color 0.3s ease;
}

.services-item:hover .service-icon {
  transform: scale(1.1);
  color: hsl(184, 100%, 50%);
  animation: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.25) icon-rotate both;
}

.service-price-container {
  display: flex;
  align-items: center;
  margin: 0;
}

.service-price {
  font-weight: bold;
  color: #444;
  position: relative;
  display: inline-block;
  padding: 4px 10px;
  background-color: rgba(79, 209, 197, 0.1);
  border-radius: 4px;
  transition: all 0.3s ease;
  margin: 0;
  font-family: "Manrope", sans-serif;
  font-optical-sizing: auto;
  font-weight: 800;
  font-style: normal;
  font-size: 1.0rem;
  letter-spacing: 0.5px;
}

.services-item:hover .service-price {
  background-color: rgba(79, 209, 197, 0.2);
  color: #222;
}

.service-description {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.service-description p {
  margin: 0;
  line-height: 1.6;
  font-size: 0.95rem;
  color: #555;
  font-family: 'Lato', sans-serif;
  border-top: 1px solid rgba(79, 209, 197, 0.2);
  padding-top: 12px;
  transition: color 0.3s ease;
}

.services-item:hover .service-description p {
  color: #333;
}

/* Important note styling */
.important-note {
  margin: 15px auto 30px;
  padding: 18px 25px;
  width: 80%;
  max-width: 800px;
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(79, 209, 197, 0.2);
  border-left: 4px solid hsl(184, 100%, 40%);
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.3s ease;
}

.important-note:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  transform: translateY(-3px);
  border-left: 6px solid hsl(184, 100%, 40%);
  background-color: rgba(255, 255, 255, 1);
}

.note-title {
  font-weight: bold;
  margin-bottom: 10px;
  margin-top: 0;
  font-size: 1.1rem;
  color: hsl(184, 100%, 35%);
  text-align: left;
  display: flex;
  align-items: center;
  font-family: 'Manrope', sans-serif;
  letter-spacing: 0.5px;
}

.note-title::before {
  content: '⚠️';
  margin-right: 10px;
  font-size: 1.2rem;
}

.note-text {
  margin-top: 0;
  font-size: 0.95rem;
  line-height: 1.6;
  color: #444;
  font-weight: 400;
  font-family: 'Lato', sans-serif;
  letter-spacing: 0.2px;
  transition: color 0.3s ease;
  text-align: left;
  margin-bottom: 0;
  padding-left: 30px; /* Align with text after icon */
}
/* Page animations */
@keyframes about-me-fadeIn {
0% {
opacity: 0;
transform: translateY(-200px);
}
100% {
pointer-events: auto; /* Enable pointer events when fully visible for back button */
opacity: 1;
transform: translateY(0);
}
}

@keyframes about-me-fadeOut {
0% {
opacity: 1;
transform: translateY(0);
}
100% {
opacity: 0;
transform: translateY(-20px);
}
}

@keyframes services-fadeIn {
  0% {
    opacity: 0;
    transform: translateY(-200px) scale(0.95);
  }
  70% {
    opacity: 1;
    transform: translateY(10px) scale(1);
  }
  100% {
    pointer-events: auto; /* Enable pointer events when fully visible for back button */
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes services-fadeOut {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px) scale(0.98);
  }
}

/* Class to hide and disable elements for page swapping */
.inactive {
pointer-events: none;
opacity: 0;
transition: opacity 0.3s ease;
}

/* Add styles for the back button */
.back-button {
  position: absolute;
  bottom: 20px;
  right: 20px;
  padding: 12px 24px;
  background: linear-gradient(to right, hsl(184, 100%, 40%), hsl(184, 100%, 45%));
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Manrope', sans-serif;
  font-weight: 600;
  letter-spacing: 1px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
}

.back-button::before {
  content: '←';
  margin-right: 10px;
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.back-button:hover {
  background: linear-gradient(to right, hsl(184, 100%, 35%), hsl(184, 100%, 40%));
  transform: translateY(-3px);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.15);
}

.back-button:hover::before {
  transform: translateX(-4px);
}

.back-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}


/* Services title styling */
.services-title {
  position: relative;
  display: inline-block;
  margin-bottom: 25px;
  padding-bottom: 12px;
  color: hsl(184, 100%, 30%);
  font-family: 'Manrope', sans-serif;
  font-weight: 800;
  font-size: 2rem;
  letter-spacing: 0.5px;
  text-transform: none;
}

.services-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, hsl(184, 100%, 40%), hsl(184, 100%, 60%));
  border-radius: 4px;
  transition: width 0.3s ease;
}

.services-title:hover::after {
  width: 120px;
}

/* Services footer and buttons container */
.services-footer {
  position: relative;
  margin-top: 40px;
  padding-bottom: 40px;
  text-align: center;
  display: flex;
  width: 100%;
}

.services-buttons-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 20px;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Base styles for both service buttons */
.services-book-button,
.services-back-button {
  flex: 1;
  padding: 16px 20px;
  color: white;
  text-align: center;
  font-family: 'Manrope', sans-serif;
  font-weight: 700;
  font-size: 16px;
  letter-spacing: 1px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.25s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 5;
  min-height: 56px;
  max-width: 300px;
}

/* Book button specific styles */
.services-book-button {
  background: linear-gradient(to right, hsl(184, 100%, 40%), hsl(184, 100%, 45%));
  text-transform: uppercase;
}

.services-book-button:hover {
  background: linear-gradient(to right, hsl(184, 100%, 35%), hsl(184, 100%, 40%));
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.services-book-button:active {
  background: linear-gradient(to right, hsl(184, 100%, 45%), hsl(184, 100%, 50%));
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Back button specific styles */
.services-back-button {
  background: linear-gradient(to right, hsl(0, 0%, 40%), hsl(0, 0%, 50%));
  display: flex;
  align-items: center;
  justify-content: center;
}

.services-back-button::before {
  content: '←';
  margin-right: 8px;
  font-size: 1.2rem;
  transition: transform 0.2s ease;
}

.services-back-button:hover {
  background: linear-gradient(to right, hsl(0, 0%, 35%), hsl(0, 0%, 45%));
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.services-back-button:hover::before {
  transform: translateX(-3px);
}

.services-back-button:active {
  background: linear-gradient(to right, hsl(0, 0%, 45%), hsl(0, 0%, 55%));
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Shine effect for services book button */
.services-book-button::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
  transform: rotate(45deg) translateY(-50%) translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.services-book-button:hover::after {
  opacity: 1;
  animation: shine 1.5s infinite;
}

@keyframes shine {
0% {
transform: rotate(45deg) translateY(-50%) translateX(-100%);
}
100% {
transform: rotate(45deg) translateY(-50%) translateX(100%);
}
}

/* ======================================================
RESPONSIVE STYLES FOR MOBILE DEVICES
====================================================== */
@media (max-width: 768px) {
  .card-container {
    top: 0;
    left: 0;
    transform: none;
    width: 100%;
    height: 100vh;
    position: fixed;
    perspective: 600px;
    transition: all 0.3s ease;
  }

  .card {
    max-width: 500px; /* Default max-width */
    width: 100%;
    height: 350px; /* Default height */
    border-radius: 0;
    transition: height 0.5s cubic-bezier(0.25, 0.1, 0.25, 1),
                max-width 0.5s cubic-bezier(0.25, 0.1, 0.25, 1),
                transform 0.05s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.3s ease;
  }

  /* Only apply these styles when in about-me-active state */
  .card.about-me-active {
    top: 0;
    height: 100vh;
    max-width: 100%;
    width: 100%;
    overflow-y: auto;
  }

  /* Only apply these styles when in services-active state */
  .card.services-active {
    top: 0;
    height: 100vh;
    max-width: 100%;
    width: 100%;
    overflow-y: auto;
  }

  /* Default state (not in about-me or services page) */
  .card:not(.about-me-active):not(.services-active) {
    max-width: 500px;
    height: 350px;
    overflow-y: hidden;
    position: relative;
    top: auto;
  }

  .card:not(.about-me-active):not(.services-active):hover {
    height: 350px;
  }

  /* About Me page specific mobile styles */
  .card.about-me-active {
    top: 0;
    height: 100vh;
    max-width: 100%;
    overflow-y: auto;
  }

  .about-me-page {
    padding: 15px;
    overflow-y: auto;
    max-height: 100vh;
  }

  .about-me-text {
    margin: 10px;
    font-size: 14px;
  }

  /* Remove the old back button styles for mobile */

  /* Services page mobile styles */
  .services-page {
    padding: 15px;
    overflow-y: auto;
    max-height: 100vh;
  }

  .services-title {
    font-size: 1.5rem;
    margin-bottom: 15px;
  }

  .important-note {
    margin: 10px 0 15px;
    padding: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transform: none;
    border-left: 4px solid hsl(184, 100%, 40%);
  }

  .important-note:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transform: none;
    border-left: 4px solid hsl(184, 100%, 40%);
    background-color: rgba(255, 255, 255, 0.95);
  }

  .note-title {
    font-size: 0.9rem;
  }

  .note-text {
    font-size: 0.8rem;
  }

  /* Remove hover effect for services title */
  .services-title:hover::after {
    width: 80px;
  }

  /* Services container for mobile */
  .services-container {
    flex-direction: column;
    padding: 0;
    margin-top: 15px;
    gap: 15px;
  }

  /* Service items for mobile */
  .services-item {
    flex: 1 1 100%;
    max-width: 100%;
    min-width: auto;
    padding: 20px;
    margin-bottom: 15px;
    border: 1px solid rgba(79, 209, 197, 0.15);
    border-radius: 12px;
  }

  /* Remove all hover effects for service items on mobile */
  .services-item {
    background-color: rgba(255, 255, 255, 0.9);
  }

  .services-item:hover {
    transform: none;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
    animation: none;
    background-color: rgba(255, 255, 255, 0.9);
  }

  .services-item:hover .service-icon {
    transform: none;
    box-shadow: none;
    animation: none;
    color: hsl(184, 100%, 30%);
  }

  .services-item::before {
    transform: none;
    box-shadow: none;
    animation: none;
    height: 4px;
    background: linear-gradient(to right, hsl(184, 100%, 40%), hsl(184, 100%, 60%));
  }

  .services-item:hover::before {
    height: 4px;
    box-shadow: none;
    opacity: 0.7;
  }

  .services-item:hover .service-icon-container {
    background-color: rgba(79, 209, 197, 0.1);
    transform: none;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
  }

  .services-item:hover h2 {
    color: hsl(184, 100%, 30%);
  }

  .services-item:hover .service-price {
    background-color: rgba(79, 209, 197, 0.1);
    color: #444;
  }

  .services-item:hover .service-description p {
    color: #555;
  }

  .service-header {
    gap: 15px;
    align-items: center;
  }

  .service-header-content {
    gap: 6px;
  }

  .service-icon-container {
    min-width: 50px;
    height: 50px;
  }

  .service-icon {
    height: 1.8rem;
    width: 1.8rem;
  }

  .services-item h2 {
    font-size: 1rem;
  }

  .service-price {
    font-size: 0.9rem;
    padding: 3px 8px;
  }

  .service-description p {
    font-size: 0.9rem;
    line-height: 1.5;
    padding-top: 10px;
  }

  /* Important note for mobile */
  .important-note {
    width: 80%;
    padding: 15px 20px;
    margin: 10px auto 20px;
  }

  /* Services footer and buttons for mobile */
  .services-footer {
    margin-top: 30px;
    padding-bottom: 30px;
    position: relative;
    bottom: 0;
    padding-top: 15px;
    z-index: 10;
  }

  .services-buttons-container {
    flex-direction: column;
    gap: 12px;
    padding: 0 15px;
  }

  .services-book-button,
  .services-back-button {
    width: 100%;
    max-width: none;
    padding: 14px 20px;
    font-size: 14px;
    border-radius: 8px;
    min-height: 50px;
  }

  /* Remove hover effects for service buttons on mobile */
  .services-book-button:hover {
    background: linear-gradient(to right, hsl(184, 100%, 40%), hsl(184, 100%, 45%));
    transform: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .services-book-button:hover::after {
    opacity: 0;
    animation: none;
  }

  .services-back-button:hover {
    background: linear-gradient(to right, hsl(0, 0%, 40%), hsl(0, 0%, 50%));
    transform: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .services-back-button:hover::before {
    transform: none;
  }
}
